import React from "react";

import {useVoiceChat} from "../logic/useVoiceChat";
import {Button} from "../Button";
import {LoadingIcon, MicIcon, MicOffIcon} from "../Icons";
import {useConversationState} from "../logic/useConversationState";
import style from "../../styles/commonStyle.module.css";
import clsx from "clsx";

export const AudioInput: React.FC = () => {
  const {muteInputAudio, unmuteInputAudio, isMuted, isVoiceChatLoading} =
    useVoiceChat();
  const {isUserTalking} = useConversationState();

  const handleMuteClick = () => {
    if (isMuted) {
      unmuteInputAudio();
    } else {
      muteInputAudio();
    }
  };

  return (
    <div>
      <Button
        className={clsx(`p-0 relative`, style.micButton)}
        disabled={isVoiceChatLoading}
        onClick={handleMuteClick}
      >
        {/* <div
          className={`absolute left-0 top-0 rounded-lg border-2 border-[#7559FF] w-full h-full ${
            isUserTalking ? "animate-ping" : ""
          }`}
        /> */}
        {isVoiceChatLoading ? (
          <LoadingIcon className="animate-spin" size={20} />
        ) : isMuted ? (
          <MicOffIcon size={20} />
        ) : (
          <MicIcon size={20} />
        )}
      </Button>
    </div>
  );
};
