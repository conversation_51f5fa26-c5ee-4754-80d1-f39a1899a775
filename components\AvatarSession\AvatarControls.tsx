import React, { useState } from "react";
import { SelectButton } from "primereact/selectbutton";

import { useVoiceChat } from "../logic/useVoiceChat";
import { Button } from "../Button";
import { useInterrupt } from "../logic/useInterrupt";

import { AudioInput } from "./AudioInput";
import { TextInput } from "./TextInput";
import style from "../../styles/commonStyle.module.css";
import { Dropdown } from "primereact/dropdown";
import { useAuthContext } from "../Prividers/AuthProvider";

interface AvatarControlsProps {
  currentLanguage: string;
  setCurrentLanguage: (language: string) => void;
}

export const AvatarControls: React.FC<AvatarControlsProps> = ({
  currentLanguage,
  setCurrentLanguage,
}: {
  currentLanguage: string;
  setCurrentLanguage: (language: string) => void;
}) => {
  const {
    isVoiceChatLoading,
    isVoiceChatActive,
    startVoiceChat,
    stopVoiceChat,
  } = useVoiceChat();
  const { interrupt } = useInterrupt();

  const auth = useAuthContext();

  const chatOptions = [
    { label: "Voice Chat", value: "voice" },
    { label: "Text Chat", value: "text" },
  ];

  const languageOptions = [
    { label: "English", value: "en" },
    { label: "Spanish", value: "sp" },
  ];

  const currentChatMode =
    isVoiceChatActive || isVoiceChatLoading ? "voice" : "text";

  const handleChatModeChange = (value: string) => {
    if (value === "voice" && !isVoiceChatActive && !isVoiceChatLoading) {
      startVoiceChat();
    } else if (value === "text" && isVoiceChatActive && !isVoiceChatLoading) {
      stopVoiceChat();
    }
  };

  return (
    <div className="flex justify-content-between relative w-full align-items-center">
      <div
        style={
          auth?.user?.username?.toLowerCase() === "<EMAIL>"
            ? { marginLeft: "2rem" }
            : { marginRight: "2rem", position: "absolute", left: "2rem" }
        }
      >
        <Button
          // severity="secondary"
          onClick={interrupt}
          className={style.interruptButton}
        >
          Interrupt
        </Button>
      </div>

      {/* <SelectButton
        value={currentChatMode}
        onChange={(e) => handleChatModeChange(e.value)}
        options={chatOptions}
        disabled={isVoiceChatLoading}
        className={isVoiceChatLoading ? "opacity-50" : ""}
      /> */}

      {(isVoiceChatActive || isVoiceChatLoading) && <AudioInput />}
      {/* <TextInput /> */}

      {auth?.user?.username?.toLowerCase() === "<EMAIL>" && (
        <Dropdown
          value={currentLanguage}
          onChange={(e) => setCurrentLanguage(e.value)}
          options={languageOptions}
          className={isVoiceChatLoading ? "opacity-50" : ""}
          style={{ width: "15rem", marginRight: "2rem" }}
        />
      )}
    </div>
  );
};
