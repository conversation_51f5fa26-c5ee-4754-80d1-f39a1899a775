.inputContainer {
  height: 2.813rem;
  border: 1px solid #51515126 !important;
  border-radius: 15px !important;
  color: var(--text-primary-color);
  font-weight: 400;
  width: 25rem;
  font-size: 1rem;
}

.errorInputContainer {
  height: 2.813rem;
  border: 1px solid #c80a0a !important;
  border-radius: 15px !important;
  color: var(--text-primary-color);
  font-weight: 400;
  width: 25rem;
  font-size: 1rem;
}

.errorInputContainer:focus {
  box-shadow: none;
}

.inputContainer:hover {
  border: 1px solid var(--text-primary-color) !important;
}

.inputContainer:focus {
  border: 1px solid var(--text-primary-color) !important;
  box-shadow: none !important;
}
.label {
  color: var(--text-primary-color);
}

.error {
  color: red;
  font-size: 13px;
}

/* Button */
.buttonContainer {
  background-color: var(--text-primary-color);
  color: #fff;
  font-weight: 700;
  height: 50px;
  border-radius: 15px !important;
}

.buttonContainer:hover {
  background-color: var(--text-primary-color) !important;
}

.secondaryButton {
  border: 1px solid #fffefe1a !important;
  background-color: transparent !important;
  font-weight: 700 !important;
  height: 50px;
  border-radius: 15px !important;
}

.secondaryButton:hover {
  border: 1px solid #fffefe1a !important;
  background-color: transparent !important;
  font-weight: 700 !important;
  height: 50px;
}

.container {
  display: flex;
  align-items: center;
}

.images {
  display: flex;
}

.circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid white;
}

.circle:not(:first-child) {
  margin-left: -16px;
}

.dots {
  display: flex;
  margin-left: 8px;
  gap: 4px;
}

.dots span {
  width: 3px;
  height: 3px;
  background-color: #d9d9d9;
  border-radius: 50%;
}
